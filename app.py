from flask import Flask, request, jsonify, render_template, redirect, url_for
from flask_cors import CORS
import os
import uuid
from datetime import datetime
from database import DatabaseManager
from qwen_api import QwenVLAPI
from config import FLASK_CONFIG

app = Flask(__name__)
CORS(app)

# 初始化組件
db_manager = DatabaseManager()
qwen_api = QwenVLAPI()

# 建立上傳資料夾
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

@app.route('/')
def index():
    """主頁面"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_image():
    """處理圖片上傳和車牌辨識"""
    try:
        if 'image' not in request.files:
            return jsonify({"success": False, "error": "沒有上傳圖片"})
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({"success": False, "error": "沒有選擇檔案"})
        
        # 儲存圖片
        filename = f"{uuid.uuid4()}_{file.filename}"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)
        
        # 獲取客戶端資訊
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        
        # 插入初始記錄
        record_id = db_manager.insert_record(
            image_name=filename,
            ip_address=ip_address,
            user_agent=user_agent,
            status='processing'
        )
        
        if not record_id:
            return jsonify({"success": False, "error": "資料庫記錄失敗"})
        
        # 調用 AI 辨識
        recognition_result = qwen_api.recognize_license_plate(filepath)
        
        if recognition_result["success"]:
            # 更新記錄
            db_manager.update_record(
                record_id=record_id,
                license_plate=recognition_result["license_plate"],
                confidence=recognition_result["confidence"],
                status='success'
            )
            
            return jsonify({
                "success": True,
                "record_id": record_id,
                "license_plate": recognition_result["license_plate"],
                "confidence": recognition_result["confidence"],
                "filename": filename
            })
        else:
            # 更新為失敗狀態
            db_manager.update_record(
                record_id=record_id,
                license_plate=None,
                confidence=0.0,
                status='failed'
            )
            
            return jsonify({
                "success": False,
                "error": recognition_result["error"],
                "record_id": record_id
            })
            
    except Exception as e:
        return jsonify({"success": False, "error": f"處理過程發生錯誤: {str(e)}"})

@app.route('/records')
def get_records():
    """獲取辨識記錄"""
    try:
        records = db_manager.get_records(limit=50)
        return jsonify({"success": True, "records": records})
    except Exception as e:
        return jsonify({"success": False, "error": f"查詢記錄失敗: {str(e)}"})

@app.route('/history')
def history():
    """歷史記錄頁面"""
    return render_template('history.html')

if __name__ == '__main__':
    # 初始化資料庫
    db_manager.init_database()
    
    # 啟動應用
    app.run(
        host=FLASK_CONFIG["host"],
        port=FLASK_CONFIG["port"],
        debug=FLASK_CONFIG["debug"]
    )
#!/usr/bin/env python3
"""
車牌辨識系統啟動腳本
"""

import sys
import os

def check_dependencies():
    """檢查依賴套件"""
    try:
        import flask
        import pymysql
        import requests
        import PIL
        print("✓ 所有依賴套件已安裝")
        return True
    except ImportError as e:
        print(f"✗ 缺少依賴套件: {e}")
        print("請執行: pip install -r requirements.txt")
        return False

def main():
    print("=" * 50)
    print("車牌辨識系統")
    print("=" * 50)
    
    # 檢查依賴
    if not check_dependencies():
        sys.exit(1)
    
    # 檢查資料庫連接
    try:
        from database import DatabaseManager
        db = DatabaseManager()
        db.init_database()
        print("✓ 資料庫連接成功")
    except Exception as e:
        print(f"✗ 資料庫連接失敗: {e}")
        print("請檢查 config.py 中的資料庫設定")
        sys.exit(1)
    
    # 啟動應用
    print("\n啟動 Web 服務...")
    print("訪問地址: http://localhost:8080")
    print("按 Ctrl+C 停止服務")
    print("-" * 50)

    from app import app
    app.run(host='0.0.0.0', port=8080, debug=True)

if __name__ == "__main__":
    main()
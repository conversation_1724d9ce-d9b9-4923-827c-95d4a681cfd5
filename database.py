import pymysql
from config import MYSQL_CONFIG
from datetime import datetime

class DatabaseManager:
    def __init__(self):
        self.config = MYSQL_CONFIG
        
    def get_connection(self):
        """建立資料庫連接"""
        return pymysql.connect(**self.config)
    
    def init_database(self):
        """初始化資料庫表格"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                # 建立車牌辨識記錄表
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS license_plate_records (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    image_name VARCHAR(255) NOT NULL,
                    license_plate VARCHAR(50),
                    confidence FLOAT,
                    recognition_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    status ENUM('success', 'failed', 'processing') DEFAULT 'processing'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """
                cursor.execute(create_table_sql)
                connection.commit()
                print("資料庫表格初始化完成")
        except Exception as e:
            print(f"資料庫初始化錯誤: {e}")
        finally:
            connection.close()
    
    def insert_record(self, image_name, license_plate=None, confidence=None, 
                     ip_address=None, user_agent=None, status='processing'):
        """插入辨識記錄"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                sql = """
                INSERT INTO license_plate_records 
                (image_name, license_plate, confidence, ip_address, user_agent, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (image_name, license_plate, confidence, 
                                   ip_address, user_agent, status))
                connection.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"插入記錄錯誤: {e}")
            return None
        finally:
            connection.close()
    
    def update_record(self, record_id, license_plate, confidence, status):
        """更新辨識結果"""
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                sql = """
                UPDATE license_plate_records 
                SET license_plate=%s, confidence=%s, status=%s 
                WHERE id=%s
                """
                cursor.execute(sql, (license_plate, confidence, status, record_id))
                connection.commit()
                return True
        except Exception as e:
            print(f"更新記錄錯誤: {e}")
            return False
        finally:
            connection.close()
    
    def get_records(self, limit=50):
        """獲取辨識記錄"""
        connection = self.get_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                SELECT * FROM license_plate_records 
                ORDER BY recognition_time DESC 
                LIMIT %s
                """
                cursor.execute(sql, (limit,))
                return cursor.fetchall()
        except Exception as e:
            print(f"查詢記錄錯誤: {e}")
            return []
        finally:
            connection.close()
# 車牌辨識系統

基於 Flask + MySQL + Qwen-VL AI 的 Web 車牌辨識系統，支援桌機和手機使用。

## 功能特色

- 🚗 **智能車牌辨識**: 使用 Qwen2.5-VL 大模型進行高精度車牌辨識
- 📱 **響應式設計**: 支援桌機、平板、手機等各種設備
- 📷 **攝像頭拍照**: 手機可直接調用攝像頭拍照辨識（支援前後置攝像頭）
- 🗄️ **資料庫記錄**: 完整記錄辨識歷史和統計資料
- 🎯 **拖拽上傳**: 支援拖拽和點擊上傳圖片
- 📊 **即時預覽**: 上傳前可預覽圖片
- 📈 **信心度評估**: 顯示 AI 辨識的信心度

## 系統架構

```
├── app.py              # Flask 主應用
├── config.py           # 配置文件
├── database.py         # 資料庫管理
├── qwen_api.py         # Qwen-VL API 接口
├── run.py              # 啟動腳本
├── requirements.txt    # Python 依賴
├── templates/          # HTML 模板
│   ├── base.html
│   ├── index.html
│   └── history.html
└── uploads/            # 圖片上傳目錄
```

## 安裝步驟

### 1. 安裝 Python 依賴

```bash
pip install -r requirements.txt
```

### 2. 配置資料庫

確保 MySQL 服務正在運行，系統會自動建立所需的資料表。

### 3. 配置 API

在 `config.py` 中已預設好 Qwen-VL API 配置，如需修改請編輯相關參數。

### 4. 啟動系統

```bash
python run.py
```

或直接執行：

```bash
python app.py
```

### 5. 訪問系統

開啟瀏覽器訪問：`http://localhost:5000`

## 使用說明

### 車牌辨識

#### 方式一：攝像頭拍照（推薦手機使用）
1. 點擊「拍照辨識」按鈕
2. 允許瀏覽器訪問攝像頭權限
3. 將車牌置於畫面中央，確保清晰可見
4. 點擊「拍照」按鈕
5. 點擊「開始辨識」等待 AI 處理結果

#### 方式二：上傳圖片
1. 點擊「上傳圖片」按鈕
2. 點擊上傳區域或拖拽圖片
3. 選擇包含車牌的圖片（支援 JPG、PNG、GIF）
4. 點擊「開始辨識」按鈕
5. 等待 AI 處理並查看結果

### 查看歷史
1. 點擊導航欄的「歷史記錄」
2. 查看所有辨識記錄
3. 包含車牌號碼、信心度、時間等資訊

## 技術規格

- **後端**: Flask (Python)
- **前端**: Bootstrap 5 + JavaScript
- **資料庫**: MySQL
- **AI 模型**: Qwen2.5-VL-32B-Instruct
- **圖片處理**: Pillow (PIL)

## 資料庫結構

```sql
CREATE TABLE license_plate_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_name VARCHAR(255) NOT NULL,
    license_plate VARCHAR(50),
    confidence FLOAT,
    recognition_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('success', 'failed', 'processing') DEFAULT 'processing'
);
```

## API 端點

- `GET /` - 主頁面
- `POST /upload` - 上傳圖片並辨識
- `GET /records` - 獲取辨識記錄
- `GET /history` - 歷史記錄頁面

## 注意事項

1. 確保網路連接正常，以便調用 Qwen-VL API
2. 上傳的圖片會暫存在 `uploads/` 目錄
3. 建議定期清理上傳的圖片檔案
4. 生產環境請修改 Flask 的 debug 模式
5. **攝像頭功能需要 HTTPS 或 localhost 環境**
6. 手機使用時建議橫向拍攝以獲得更好的車牌辨識效果

## 故障排除

### 資料庫連接失敗
- 檢查 MySQL 服務是否啟動
- 確認 `config.py` 中的資料庫配置正確

### API 調用失敗
- 檢查網路連接
- 確認 API token 是否有效
- 查看 API 配額是否足夠

### 圖片上傳失敗
- 檢查 `uploads/` 目錄權限
- 確認圖片格式是否支援

### 攝像頭無法使用
- 確保使用 HTTPS 或 localhost 訪問
- 檢查瀏覽器攝像頭權限設定
- 確認設備有可用的攝像頭
- 嘗試重新整理頁面並重新授權
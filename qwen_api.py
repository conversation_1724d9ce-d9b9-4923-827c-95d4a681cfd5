import requests
import base64
import json
from config import QWEN_API_CONFIG

class QwenVLAPI:
    def __init__(self):
        self.config = QWEN_API_CONFIG
        
    def encode_image_to_base64(self, image_path):
        """將圖片編碼為 base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"圖片編碼錯誤: {e}")
            return None
    
    def recognize_license_plate(self, image_path):
        """調用 Qwen-VL API 辨識車牌"""
        try:
            # 編碼圖片
            base64_image = self.encode_image_to_base64(image_path)
            if not base64_image:
                return {"success": False, "error": "圖片編碼失敗"}
            
            # 準備 API 請求
            headers = {
                "Authorization": f"Bearer {self.config['api_token']}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.config["model_name"],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "請仔細分析這張圖片中的車牌號碼。如果能看到車牌，請只回傳車牌號碼（不要包含其他文字）。如果看不到車牌或無法辨識，請回傳'無法辨識'。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": self.config["max_tokens"],
                "temperature": self.config["temperature"],
                "top_p": self.config["top_p"]
            }
            
            # 發送請求
            response = requests.post(
                self.config["api_url"],
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                license_plate = result["choices"][0]["message"]["content"].strip()
                
                # 簡單的信心度計算（基於回應長度和內容）
                confidence = self.calculate_confidence(license_plate)
                
                return {
                    "success": True,
                    "license_plate": license_plate,
                    "confidence": confidence,
                    "raw_response": result
                }
            else:
                return {
                    "success": False,
                    "error": f"API 請求失敗: {response.status_code}",
                    "details": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"辨識過程發生錯誤: {str(e)}"
            }
    
    def calculate_confidence(self, license_plate):
        """計算辨識信心度"""
        if license_plate == "無法辨識" or not license_plate:
            return 0.0
        
        # 基本的信心度計算邏輯
        confidence = 0.5  # 基礎分數
        
        # 長度檢查（台灣車牌通常 6-8 字元）
        if 6 <= len(license_plate) <= 8:
            confidence += 0.3
        
        # 包含數字和字母
        has_digit = any(c.isdigit() for c in license_plate)
        has_alpha = any(c.isalpha() for c in license_plate)
        
        if has_digit and has_alpha:
            confidence += 0.2
        
        return min(confidence, 1.0)
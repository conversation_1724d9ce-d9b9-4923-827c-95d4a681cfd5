{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-camera"></i> 車牌辨識</h4>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <!-- 攝像頭和上傳選項 -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <button type="button" class="btn btn-camera w-100" id="cameraBtn">
                                <i class="fas fa-camera"></i> 拍照辨識
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-info w-100" id="uploadBtn">
                                <i class="fas fa-upload"></i> 上傳圖片
                            </button>
                        </div>
                    </div>

                    <!-- 攝像頭區域 -->
                    <div id="cameraArea" class="camera-controls" style="display: none;">
                        <div class="text-center mb-3">
                            <video id="video" width="100%" height="300" autoplay playsinline muted></video>
                            <canvas id="canvas" style="display: none;"></canvas>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="button" class="btn btn-primary w-100" id="captureBtn">
                                    <i class="fas fa-camera"></i> 拍照
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-secondary w-100" id="closeCameraBtn">
                                    <i class="fas fa-times"></i> 關閉
                                </button>
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> 
                                請將車牌置於畫面中央，確保清晰可見
                            </small>
                        </div>
                    </div>

                    <!-- 上傳區域 -->
                    <div class="upload-area" id="uploadArea" style="display: none;">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>點擊或拖拽圖片到此處</h5>
                        <p class="text-muted">支援 JPG, PNG, GIF 格式</p>
                        <input type="file" id="imageInput" name="image" accept="image/*" style="display: none;">
                    </div>
                    
                    <div id="imagePreview" class="mt-3" style="display: none;">
                        <img id="previewImg" class="img-fluid rounded" style="max-height: 300px;">
                    </div>
                    
                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary btn-lg w-100" id="submitBtn">
                            <i class="fas fa-search"></i> 開始辨識
                        </button>
                    </div>
                </form>
                
                <div id="loadingDiv" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">辨識中...</span>
                    </div>
                    <p class="mt-2">AI 正在辨識車牌，請稍候...</p>
                </div>
                
                <div id="resultDiv" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> 辨識結果</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>車牌號碼：</strong>
                                <span id="licensePlate" class="fs-4 text-primary"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>信心度：</strong>
                                <span id="confidence"></span>%
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="errorDiv" class="mt-4" style="display: none;">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> 辨識失敗</h5>
                        <p id="errorMessage"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const imageInput = document.getElementById('imageInput');
    const uploadForm = document.getElementById('uploadForm');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const submitBtn = document.getElementById('submitBtn');
    const loadingDiv = document.getElementById('loadingDiv');
    const resultDiv = document.getElementById('resultDiv');
    const errorDiv = document.getElementById('errorDiv');
    
    // 攝像頭相關元素
    const cameraBtn = document.getElementById('cameraBtn');
    const uploadBtn = document.getElementById('uploadBtn');
    const cameraArea = document.getElementById('cameraArea');
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const captureBtn = document.getElementById('captureBtn');
    const closeCameraBtn = document.getElementById('closeCameraBtn');
    
    let stream = null;
    let capturedImageBlob = null;

    // 攝像頭按鈕事件
    cameraBtn.addEventListener('click', async () => {
        try {
            // 檢查瀏覽器支援
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('瀏覽器不支援攝像頭功能');
            }

            // 先嘗試後置攝像頭，失敗則使用前置攝像頭
            let constraints = { 
                video: { 
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                } 
            };

            try {
                stream = await navigator.mediaDevices.getUserMedia(constraints);
            } catch (envError) {
                console.log('後置攝像頭不可用，嘗試前置攝像頭');
                constraints.video.facingMode = 'user';
                stream = await navigator.mediaDevices.getUserMedia(constraints);
            }

            video.srcObject = stream;
            
            // 顯示攝像頭區域，隱藏上傳區域
            cameraArea.style.display = 'block';
            uploadArea.style.display = 'none';
            imagePreview.style.display = 'none';
            resultDiv.style.display = 'none';
            errorDiv.style.display = 'none';
            
        } catch (error) {
            console.error('無法訪問攝像頭:', error);
            let errorMsg = '無法訪問攝像頭';
            
            if (error.name === 'NotAllowedError') {
                errorMsg = '攝像頭權限被拒絕，請在瀏覽器設定中允許攝像頭權限';
            } else if (error.name === 'NotFoundError') {
                errorMsg = '找不到攝像頭設備';
            } else if (error.name === 'NotSupportedError') {
                errorMsg = '瀏覽器不支援攝像頭功能';
            }
            
            alert(errorMsg + '，請使用上傳功能');
        }
    });

    // 上傳按鈕事件
    uploadBtn.addEventListener('click', () => {
        // 關閉攝像頭
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        
        // 顯示上傳區域，隱藏攝像頭區域
        uploadArea.style.display = 'block';
        cameraArea.style.display = 'none';
        imagePreview.style.display = 'none';
        resultDiv.style.display = 'none';
        errorDiv.style.display = 'none';
    });

    // 拍照按鈕事件
    captureBtn.addEventListener('click', () => {
        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        // 將視頻畫面繪製到 canvas
        context.drawImage(video, 0, 0);
        
        // 轉換為 blob
        canvas.toBlob((blob) => {
            capturedImageBlob = blob;
            
            // 顯示預覽
            const url = URL.createObjectURL(blob);
            previewImg.src = url;
            imagePreview.style.display = 'block';
            
            // 關閉攝像頭
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            cameraArea.style.display = 'none';
            
            // 啟用提交按鈕
            submitBtn.disabled = false;
        }, 'image/jpeg', 0.8);
    });

    // 關閉攝像頭按鈕事件
    closeCameraBtn.addEventListener('click', () => {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        cameraArea.style.display = 'none';
    });

    // 點擊上傳區域
    uploadArea.addEventListener('click', () => {
        imageInput.click();
    });

    // 拖拽功能
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            imageInput.files = files;
            handleFileSelect(files[0]);
        }
    });

    // 檔案選擇
    imageInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    function handleFileSelect(file) {
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
                submitBtn.disabled = false;
                capturedImageBlob = null; // 清除拍照的圖片
            };
            reader.readAsDataURL(file);
        }
    }

    // 表單提交
    uploadForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // 檢查是否有圖片（上傳的或拍照的）
        if (!imageInput.files[0] && !capturedImageBlob) {
            alert('請選擇圖片或拍照');
            return;
        }

        // 隱藏之前的結果
        resultDiv.style.display = 'none';
        errorDiv.style.display = 'none';
        loadingDiv.style.display = 'block';
        submitBtn.disabled = true;

        const formData = new FormData();
        
        // 使用拍照的圖片或上傳的圖片
        if (capturedImageBlob) {
            formData.append('image', capturedImageBlob, 'camera_capture.jpg');
        } else {
            formData.append('image', imageInput.files[0]);
        }

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            loadingDiv.style.display = 'none';

            if (result.success) {
                document.getElementById('licensePlate').textContent = result.license_plate;
                document.getElementById('confidence').textContent = (result.confidence * 100).toFixed(1);
                resultDiv.style.display = 'block';
            } else {
                document.getElementById('errorMessage').textContent = result.error;
                errorDiv.style.display = 'block';
            }
        } catch (error) {
            loadingDiv.style.display = 'none';
            document.getElementById('errorMessage').textContent = '網路錯誤，請稍後再試';
            errorDiv.style.display = 'block';
        }

        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
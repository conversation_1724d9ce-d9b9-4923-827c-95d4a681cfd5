{% extends "base.html" %}

{% block title %}歷史記錄 - 車牌辨識系統{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-history"></i> 辨識歷史記錄</h4>
                <button class="btn btn-light btn-sm" onclick="loadRecords()">
                    <i class="fas fa-refresh"></i> 重新整理
                </button>
            </div>
            <div class="card-body">
                <div id="loadingRecords" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                    <p class="mt-2">載入記錄中...</p>
                </div>
                
                <div id="recordsTable" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>圖片名稱</th>
                                    <th>車牌號碼</th>
                                    <th>信心度</th>
                                    <th>狀態</th>
                                    <th>辨識時間</th>
                                    <th>IP 位址</th>
                                </tr>
                            </thead>
                            <tbody id="recordsBody">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div id="noRecords" class="text-center text-muted" style="display: none;">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <h5>暫無記錄</h5>
                    <p>還沒有任何車牌辨識記錄</p>
                </div>
                
                <div id="errorRecords" class="alert alert-danger" style="display: none;">
                    <h5><i class="fas fa-exclamation-triangle"></i> 載入失敗</h5>
                    <p id="errorRecordsMessage"></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadRecords();
});

async function loadRecords() {
    const loadingDiv = document.getElementById('loadingRecords');
    const tableDiv = document.getElementById('recordsTable');
    const noRecordsDiv = document.getElementById('noRecords');
    const errorDiv = document.getElementById('errorRecords');
    const tbody = document.getElementById('recordsBody');

    // 顯示載入狀態
    loadingDiv.style.display = 'block';
    tableDiv.style.display = 'none';
    noRecordsDiv.style.display = 'none';
    errorDiv.style.display = 'none';

    try {
        const response = await fetch('/records');
        const result = await response.json();

        loadingDiv.style.display = 'none';

        if (result.success) {
            if (result.records.length === 0) {
                noRecordsDiv.style.display = 'block';
            } else {
                tbody.innerHTML = '';
                result.records.forEach(record => {
                    const row = createRecordRow(record);
                    tbody.appendChild(row);
                });
                tableDiv.style.display = 'block';
            }
        } else {
            document.getElementById('errorRecordsMessage').textContent = result.error;
            errorDiv.style.display = 'block';
        }
    } catch (error) {
        loadingDiv.style.display = 'none';
        document.getElementById('errorRecordsMessage').textContent = '網路錯誤，請稍後再試';
        errorDiv.style.display = 'block';
    }
}

function createRecordRow(record) {
    const row = document.createElement('tr');
    
    // 狀態顏色
    let statusBadge = '';
    switch(record.status) {
        case 'success':
            statusBadge = '<span class="badge bg-success">成功</span>';
            break;
        case 'failed':
            statusBadge = '<span class="badge bg-danger">失敗</span>';
            break;
        case 'processing':
            statusBadge = '<span class="badge bg-warning">處理中</span>';
            break;
    }
    
    // 信心度顯示
    const confidence = record.confidence ? (record.confidence * 100).toFixed(1) + '%' : '-';
    
    // 車牌號碼顯示
    const licensePlate = record.license_plate || '-';
    
    // 時間格式化
    const time = new Date(record.recognition_time).toLocaleString('zh-TW');
    
    row.innerHTML = `
        <td>${record.id}</td>
        <td><code>${record.image_name}</code></td>
        <td><strong class="text-primary">${licensePlate}</strong></td>
        <td>${confidence}</td>
        <td>${statusBadge}</td>
        <td>${time}</td>
        <td><small>${record.ip_address || '-'}</small></td>
    `;
    
    return row;
}
</script>
{% endblock %}
# 資料庫配置
MYSQL_CONFIG = {
    "host": "ftp2.agbnielsen.com.tw",
    "port": 3306,
    "user": "root",
    "password": "22318430",
    "database": "lpr_system",
    "charset": "utf8mb4"
}

# Qwen2.5-VL API 配置
QWEN_API_CONFIG = {
    "api_url": "https://api.siliconflow.cn/v1/chat/completions",
    "api_token": "sk-vtwjfafhyenvsejenpoeizoxurvuueuaqwimfychuwlmudle",
    "model_name": "Qwen/Qwen2.5-VL-32B-Instruct",
    "max_tokens": 100,
    "temperature": 0.1,
    "top_p": 0.7,
    "thinking_budget": 1024
}

# Flask 配置
FLASK_CONFIG = {
    "host": "0.0.0.0",
    "port": 8080,  # 使用常見的 Web 應用端口
    "debug": True
}